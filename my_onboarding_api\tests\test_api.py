"""
Comprehensive API tests for the onboarding service.

This module combines all API-related tests including:
- API routes
- API flows
- Endpoint functionality
"""

import pytest
from fastapi import status
from unittest.mock import Mock, patch

class TestHealthEndpoint:
    """Test the health check endpoint."""
    
    def test_health_check(self, client):
        """Test health check returns correct status."""
        response = client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
        assert "services" in data


class TestOnboardingEndpoint:
    """Test the onboarding intent classification endpoint."""
    
    def test_onboarding_intent_success(self, client, mock_intent_service, sample_message_request):
        """Test successful intent classification."""
        response = client.post("/onboarding", json=sample_message_request)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["input"] == sample_message_request["message"]
        assert "intent" in data
        assert "message" in data
    
    def test_onboarding_intent_empty_message(self, client, mock_intent_service):
        """Test intent classification with empty message."""
        response = client.post("/onboarding", json={"message": ""})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_CONTENT
    
    def test_onboarding_intent_invalid_request(self, client, mock_intent_service):
        """Test intent classification with invalid request."""
        response = client.post("/onboarding", json={})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_CONTENT


class TestAPIFlow:
    """Test complete API flows including multiple requests."""
    
    def test_complete_signup_flow(self, client, mock_intent_service, mock_gemini_service):
        """Test complete signup flow through the API."""
        # Start signup flow
        response = client.post(
            "/onboarding",
            json={"message": "I want to sign up"}
        )
        assert response.status_code == status.HTTP_200_OK
        
        # Continue with email
        session_id = response.headers.get("session_id")
        response = client.post(
            "/onboarding",
            json={"message": "<EMAIL>"},
            headers={"session_id": session_id}
        )
        assert response.status_code == status.HTTP_200_OK
        
        # Verify the flow state
        data = response.json()
        assert "next_step" in data
        assert data["next_step"] == "collect_name"


class TestGeminiModelsEndpoint:
    """Test the Gemini models listing endpoint."""
    
    def test_list_gemini_models_success(self, client, mock_gemini_service):
        """Test successful model listing."""
        # The mock is already set up in the fixture
        response = client.get("/api/models/gemini")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        assert "name" in data[0]
        assert "description" in data[0]


# Fixtures for the tests
@pytest.fixture
def client():
    """Test client fixture."""
    from fastapi.testclient import TestClient
    from app.main import app
    return TestClient(app)

@pytest.fixture
def mock_intent_service():
    """Mock intent service fixture."""
    from app.models.responses import IntentClassificationResult
    from unittest.mock import MagicMock
    
    # Create a mock for the pipeline
    mock_pipeline = MagicMock()
    mock_pipeline.return_value = [
        [{"label": "signup", "score": 0.95}, {"label": "greeting", "score": 0.05}]
    ]
    
    # Create a mock for the intent service
    mock_service = MagicMock()
    
    # Set up the return values for the mock
    mock_service.classify_intent.return_value = [
        IntentClassificationResult(label="signup", score=0.95),
        IntentClassificationResult(label="greeting", score=0.05)
    ]
    mock_service.get_top_intent.return_value = "signup"
    
    # Patch both the pipeline and the intent_service
    with patch('app.services.intent_service.pipeline', mock_pipeline), \
         patch('app.services.intent_service.intent_service', mock_service):
        yield mock_service

@pytest.fixture
def mock_gemini_service():
    """Mock Gemini service fixture."""
    # Create a mock for the Gemini service
    mock_service = Mock()
    
    # Set up the return values for the mock
    mock_service.list_models.return_value = [
        {"name": "gemini-pro", "description": "Gemini Pro model"},
        {"name": "gemini-pro-vision", "description": "Gemini Pro Vision model"}
    ]
    
    # Patch the gemini_service module to return our mock
    with patch('app.services.gemini_service.gemini_service', mock_service):
        yield mock_service

@pytest.fixture
def sample_message_request():
    """Sample message request fixture."""
    return {
        "message": "I want to sign up for an account",
        "session_id": "test-session-123"
    }

if __name__ == "__main__":
    pytest.main([__file__])
