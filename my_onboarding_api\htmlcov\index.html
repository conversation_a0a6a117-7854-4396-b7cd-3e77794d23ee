<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">50%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.11.0">coverage.py v7.11.0</a>,
            created at 2025-10-30 13:01 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html">app\api\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html">app\api\auth.py</a></td>
                <td>30</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="26 30">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html">app\api\routes.py</a></td>
                <td>274</td>
                <td>234</td>
                <td>0</td>
                <td class="right" data-ratio="40 274">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html">app\core\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td>59</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="55 59">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html">app\core\exceptions.py</a></td>
                <td>42</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="30 42">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html">app\core\logging.py</a></td>
                <td>39</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="38 39">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app\main.py</a></td>
                <td>74</td>
                <td>21</td>
                <td>3</td>
                <td class="right" data-ratio="53 74">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430___init___py.html">app\middleware\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_logging_middleware_py.html">app\middleware\logging_middleware.py</a></td>
                <td>25</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="21 25">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html">app\middleware\security_middleware.py</a></td>
                <td>58</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="52 58">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html">app\models\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html">app\models\requests.py</a></td>
                <td>148</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="105 148">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html">app\models\responses.py</a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html">app\models\session.py</a></td>
                <td>71</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="70 71">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html">app\services\auth_service.py</a></td>
                <td>106</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="51 106">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html">app\services\city_service.py</a></td>
                <td>82</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="59 82">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html">app\services\gemini_service.py</a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html">app\services\intent_service.py</a></td>
                <td>86</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="61 86">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html">app\services\session_service.py</a></td>
                <td>284</td>
                <td>264</td>
                <td>0</td>
                <td class="right" data-ratio="20 284">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_settings_py.html">app\settings.py</a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html">app\utils\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html">app\utils\security.py</a></td>
                <td>54</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="49 54">91%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1547</td>
                <td>776</td>
                <td>3</td>
                <td class="right" data-ratio="771 1547">50%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.11.0">coverage.py v7.11.0</a>,
            created at 2025-10-30 13:01 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_a7b07432402c05f1_security_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_5f5a17c013354698___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
