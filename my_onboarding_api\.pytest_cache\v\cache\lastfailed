{"tests/test_api_routes.py::TestOnboardingEndpoint::test_onboarding_intent_success": true, "tests/test_api_routes.py::TestGeminiChatEndpoint::test_gemini_chat_success": true, "tests/test_api_routes.py::TestCombinedChatEndpoint::test_combined_chat_success": true, "tests/test_api_routes.py::TestCombinedChatEndpoint::test_combined_chat_missing_session_id": true, "tests/test_api_routes.py::TestCombinedChatEndpoint::test_combined_chat_login_flow": true, "tests/test_signup_api.py::TestAuthenticationServiceSignup::test_signup_api_failure": true, "tests/test_login_flow.py::test_health_check": true, "tests/test_api.py::TestAPIFlow::test_complete_signup_flow": true, "tests/test_api.py::TestGeminiModelsEndpoint::test_list_gemini_models_success": true}