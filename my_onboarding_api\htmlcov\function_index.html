<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">50%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.11.0">coverage.py v7.11.0</a>,
            created at 2025-10-30 12:32 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app\__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html">app\api\__init__.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html#t31">app\api\auth.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html#t31"><data value='login_for_access_token'>login_for_access_token</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html#t79">app\api\auth.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html#t79"><data value='login_for_token'>login_for_token</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html">app\api\auth.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t53">app\api\routes.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t53"><data value='onboarding_intent'>onboarding_intent</data></a></td>
                <td>23</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="8 23">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t126">app\api\routes.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t126"><data value='gemini_chat'>gemini_chat</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t189">app\api\routes.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t189"><data value='gemini_chat_with_intent'>gemini_chat_with_intent</data></a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t328">app\api\routes.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t328"><data value='handle_active_flow'>_handle_active_flow</data></a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t657">app\api\routes.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t657"><data value='debug_intent_classification'>debug_intent_classification</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t698">app\api\routes.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html#t698"><data value='list_gemini_models'>list_gemini_models</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html">app\api\routes.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html">app\core\__init__.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t83">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t83"><data value='validate_hf_token'>Settings.validate_hf_token</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t91">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t91"><data value='validate_gemini_api_key'>Settings.validate_gemini_api_key</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t99">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t99"><data value='validate_confidence_threshold'>Settings.validate_confidence_threshold</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t107">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t107"><data value='validate_log_level'>Settings.validate_log_level</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>46</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="46 46">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t14">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t14"><data value='init__'>OnboardingAPIException.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t32">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t32"><data value='init__'>ExternalServiceError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t61">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t61"><data value='init__'>AuthenticationError.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t83">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t83"><data value='init__'>RateLimitError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t93">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t93"><data value='create_http_exception'>create_http_exception</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t123">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t123"><data value='create_validation_error'>create_validation_error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t133">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t133"><data value='internal_server_error'>internal_server_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t141">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t141"><data value='service_unavailable_error'>service_unavailable_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t150">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t150"><data value='rate_limit_error'>rate_limit_error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html#t28">app\core\logging.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html#t28"><data value='format'>CustomFormatter.format</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html#t43">app\core\logging.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html#t43"><data value='setup_logging'>setup_logging</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html#t88">app\core\logging.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html#t88"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html#t102">app\core\logging.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html#t102"><data value='log_request'>log_request</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html">app\core\logging.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t30">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t30"><data value='lifespan'>lifespan</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t61">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t61"><data value='create_app'>create_app</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t100">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t100"><data value='health_check'>create_app.health_check</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t129">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t129"><data value='add_exception_handlers'>add_exception_handlers</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t138">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t138"><data value='onboarding_exception_handler'>add_exception_handlers.onboarding_exception_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t151">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t151"><data value='validation_exception_handler'>add_exception_handlers.validation_exception_handler</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t164">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t164"><data value='http_exception_handler'>add_exception_handlers.http_exception_handler</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t177">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t177"><data value='value_error_handler'>add_exception_handlers.value_error_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t190">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t190"><data value='general_exception_handler'>add_exception_handlers.general_exception_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430___init___py.html">app\middleware\__init__.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_logging_middleware_py.html#t20">app\middleware\logging_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_logging_middleware_py.html#t20"><data value='dispatch'>LoggingMiddleware.dispatch</data></a></td>
                <td>17</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="13 17">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_logging_middleware_py.html">app\middleware\logging_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_logging_middleware_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t22">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t22"><data value='init__'>RateLimitMiddleware.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t40">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t40"><data value='dispatch'>RateLimitMiddleware.dispatch</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t84">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t84"><data value='get_client_ip'>RateLimitMiddleware._get_client_ip</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t98">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t98"><data value='is_request_allowed'>RateLimitMiddleware._is_request_allowed</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t110">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t110"><data value='record_request'>RateLimitMiddleware._record_request</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t115">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t115"><data value='get_remaining_requests'>RateLimitMiddleware._get_remaining_requests</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t124">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t124"><data value='dispatch'>SecurityHeadersMiddleware.dispatch</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html">app\models\__init__.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t25">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t25"><data value='validate_message'>MessageRequest.validate_message</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t54">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t54"><data value='validate_email_format'>LoginRequest.validate_email_format</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t62">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t62"><data value='validate_password'>LoginRequest.validate_password</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t234">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t234"><data value='validate_name_fields'>SignupRequest.validate_name_fields</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t252">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t252"><data value='validate_email_format'>SignupRequest.validate_email_format</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t260">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t260"><data value='validate_password'>SignupRequest.validate_password</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t270">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t270"><data value='validate_phone_number'>SignupRequest.validate_phone_number</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t284">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t284"><data value='validate_zip_code'>SignupRequest.validate_zip_code</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t300">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t300"><data value='validate_birthday'>SignupRequest.validate_birthday</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t314">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t314"><data value='validate_cities_array'>SignupRequest.validate_cities_array</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t330">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t330"><data value='validate_city_name'>SignupRequest.validate_city_name</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t347">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t347"><data value='validate_city_names_array'>SignupRequest.validate_city_names_array</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>71</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="71 71">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html#t106">app\models\session.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html#t106"><data value='to_dict'>UserData.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html">app\models\session.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>70</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="70 70">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html#t23">app\services\auth_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html#t23"><data value='init__'>AuthenticationService.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html#t30">app\services\auth_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html#t30"><data value='login'>AuthenticationService.login</data></a></td>
                <td>25</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="12 25">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html#t125">app\services\auth_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html#t125"><data value='signup'>AuthenticationService.signup</data></a></td>
                <td>57</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="32 57">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html#t325">app\services\auth_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html#t325"><data value='validate_credentials'>AuthenticationService.validate_credentials</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html">app\services\auth_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t16">app\services\city_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t16"><data value='init__'>CityService.__init__</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t43">app\services\city_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t43"><data value='find_city_by_name'>CityService.find_city_by_name</data></a></td>
                <td>26</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="17 26">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t107">app\services\city_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t107"><data value='validate_city_name_and_get_id'>CityService.validate_city_name_and_get_id</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t129">app\services\city_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t129"><data value='validate_city_names_and_get_ids'>CityService.validate_city_names_and_get_ids</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t150">app\services\city_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t150"><data value='convert_dynamo_item'>CityService._convert_dynamo_item</data></a></td>
                <td>11</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="5 11">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t170">app\services\city_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t170"><data value='get_city_service'>get_city_service</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html">app\services\city_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t21">app\services\gemini_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t21"><data value='init__'>GeminiService.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t26">app\services\gemini_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t26"><data value='initialize_client'>GeminiService._initialize_client</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t46">app\services\gemini_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t46"><data value='generate_response'>GeminiService.generate_response</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t92">app\services\gemini_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t92"><data value='generate_chat_response'>GeminiService.generate_chat_response</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t125">app\services\gemini_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t125"><data value='list_available_models'>GeminiService.list_available_models</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t159">app\services\gemini_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t159"><data value='is_service_ready'>GeminiService.is_service_ready</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html">app\services\gemini_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t22">app\services\intent_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t22"><data value='init__'>IntentClassificationService.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t27">app\services\intent_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t27"><data value='initialize_pipeline'>IntentClassificationService._initialize_pipeline</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t52">app\services\intent_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t52"><data value='classify_intent'>IntentClassificationService.classify_intent</data></a></td>
                <td>43</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="31 43">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t148">app\services\intent_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t148"><data value='get_top_intent'>IntentClassificationService.get_top_intent</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t174">app\services\intent_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t174"><data value='is_pipeline_ready'>IntentClassificationService.is_pipeline_ready</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t178">app\services\intent_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t178"><data value='test_pipeline_output'>IntentClassificationService.test_pipeline_output</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html">app\services\intent_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t21">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t21"><data value='init__'>SessionService.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t27">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t27"><data value='get_session'>SessionService.get_session</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t49">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t49"><data value='update_session'>SessionService.update_session</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t61">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t61"><data value='start_flow'>SessionService.start_flow</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t91">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t91"><data value='advance_flow'>SessionService.advance_flow</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t226">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t226"><data value='complete_flow'>SessionService.complete_flow</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t281">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t281"><data value='reset_session'>SessionService.reset_session</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t292">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t292"><data value='is_flow_active'>SessionService.is_flow_active</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t305">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t305"><data value='validate_field_value'>SessionService._validate_field_value</data></a></td>
                <td>97</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="0 97">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t523">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t523"><data value='get_flow_message'>SessionService.get_flow_message</data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_settings_py.html">app\settings.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html">app\utils\__init__.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t17">app\utils\security.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t17"><data value='sanitize_input'>sanitize_input</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t45">app\utils\security.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t45"><data value='validate_email'>validate_email</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t63">app\utils\security.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t63"><data value='validate_url'>validate_url</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t91">app\utils\security.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t91"><data value='is_safe_string'>is_safe_string</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t115">app\utils\security.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t115"><data value='detect_potential_injection'>detect_potential_injection</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t148">app\utils\security.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t148"><data value='generate_session_id'>generate_session_id</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t159">app\utils\security.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html#t159"><data value='mask_sensitive_data'>mask_sensitive_data</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html">app\utils\security.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1547</td>
                <td>766</td>
                <td>3</td>
                <td class="right" data-ratio="781 1547">50%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.11.0">coverage.py v7.11.0</a>,
            created at 2025-10-30 12:32 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
