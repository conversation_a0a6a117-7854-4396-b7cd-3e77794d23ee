<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">50%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.11.0">coverage.py v7.11.0</a>,
            created at 2025-10-30 12:32 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app\__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html">app\api\__init__.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html#t24">app\api\auth.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html#t24"><data value='Token'>Token</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html">app\api\auth.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="26 30">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html">app\api\routes.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>274</td>
                <td>237</td>
                <td>0</td>
                <td class="right" data-ratio="37 274">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html">app\core\__init__.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t17">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t17"><data value='Settings'>Settings</data></a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="46 46">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t11">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t11"><data value='OnboardingAPIException'>OnboardingAPIException</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t24">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t24"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t29">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t29"><data value='ExternalServiceError'>ExternalServiceError</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t44">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t44"><data value='IntentClassificationError'>IntentClassificationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t49">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t49"><data value='SessionError'>SessionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t54">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t54"><data value='ValidationError'>ValidationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t59">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t59"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t80">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html#t80"><data value='RateLimitError'>RateLimitError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html">app\core\exceptions.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="23 33">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html#t15">app\core\logging.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html#t15"><data value='CustomFormatter'>CustomFormatter</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html">app\core\logging.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app\main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>21</td>
                <td>3</td>
                <td class="right" data-ratio="53 74">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430___init___py.html">app\middleware\__init__.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_logging_middleware_py.html#t17">app\middleware\logging_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_logging_middleware_py.html#t17"><data value='LoggingMiddleware'>LoggingMiddleware</data></a></td>
                <td>17</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="13 17">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_logging_middleware_py.html">app\middleware\logging_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_logging_middleware_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t19">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t19"><data value='RateLimitMiddleware'>RateLimitMiddleware</data></a></td>
                <td>32</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="27 32">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t121">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html#t121"><data value='SecurityHeadersMiddleware'>SecurityHeadersMiddleware</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html">app\middleware\security_middleware.py</a></td>
                <td class="name left"><a href="z_de3c7ac9f91c9430_security_middleware_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html">app\models\__init__.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t12">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t12"><data value='MessageRequest'>MessageRequest</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t38">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t38"><data value='LoginRequest'>LoginRequest</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t71">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html#t71"><data value='SignupRequest'>SignupRequest</data></a></td>
                <td>64</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="23 64">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html">app\models\requests.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_requests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="71 71">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t11">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t11"><data value='BaseResponse'>BaseResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t18">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t18"><data value='ErrorResponse'>ErrorResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t26">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t26"><data value='IntentClassificationResult'>IntentClassificationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t33">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t33"><data value='OnboardingResponse'>OnboardingResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t40">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t40"><data value='GeminiChatResponse'>GeminiChatResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t47">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t47"><data value='CombinedChatResponse'>CombinedChatResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t56">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t56"><data value='AuthenticationResponse'>AuthenticationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t63">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t63"><data value='SignupResponse'>SignupResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t70">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t70"><data value='GeminiModel'>GeminiModel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t77">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t77"><data value='GeminiModelsResponse'>GeminiModelsResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t83">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html#t83"><data value='SessionFlowResponse'>SessionFlowResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html">app\models\responses.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_responses_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html#t12">app\models\session.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html#t12"><data value='FlowType'>FlowType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html#t18">app\models\session.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html#t18"><data value='FlowStep'>FlowStep</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html#t49">app\models\session.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html#t49"><data value='SessionData'>SessionData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html#t60">app\models\session.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html#t60"><data value='UserData'>UserData</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html">app\models\session.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_session_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="70 70">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html#t20">app\services\auth_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html#t20"><data value='AuthenticationService'>AuthenticationService</data></a></td>
                <td>91</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="48 91">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html">app\services\auth_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_auth_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t13">app\services\city_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html#t13"><data value='CityService'>CityService</data></a></td>
                <td>64</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="41 64">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html">app\services\city_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_city_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t18">app\services\gemini_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html#t18"><data value='GeminiService'>GeminiService</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html">app\services\gemini_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_gemini_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t19">app\services\intent_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html#t19"><data value='IntentClassificationService'>IntentClassificationService</data></a></td>
                <td>70</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="45 70">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html">app\services\intent_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_intent_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t18">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html#t18"><data value='SessionService'>SessionService</data></a></td>
                <td>265</td>
                <td>264</td>
                <td>0</td>
                <td class="right" data-ratio="1 265">1%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html">app\services\session_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_session_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_settings_py.html#t6">app\settings.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_settings_py.html#t6"><data value='Settings'>Settings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_settings_py.html#t26">app\settings.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_settings_py.html#t26"><data value='Config'>Settings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_settings_py.html">app\settings.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html">app\utils\__init__.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html">app\utils\security.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="49 54">91%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1547</td>
                <td>766</td>
                <td>3</td>
                <td class="right" data-ratio="781 1547">50%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.11.0">coverage.py v7.11.0</a>,
            created at 2025-10-30 12:32 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
