{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.11.0", "globals": "00c0e33a217bb466b9ce7e0dd98888ff", "files": {"z_5f5a17c013354698___init___py": {"hash": "1054cdf93ef549a3c39d6c94ac8c567f", "index": {"url": "z_5f5a17c013354698___init___py.html", "file": "app\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b___init___py": {"hash": "6b53d5e5bc9fa44fcca96c3485fb1938", "index": {"url": "z_4a9cca768ff3c21b___init___py.html", "file": "app\\api\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b_routes_py": {"hash": "0d697da9c7d3d7018719677a3203b916", "index": {"url": "z_4a9cca768ff3c21b_routes_py.html", "file": "app\\api\\routes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 274, "n_excluded": 0, "n_missing": 237, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2___init___py": {"hash": "b2949091b74a97d1fb0442638c0e5d19", "index": {"url": "z_adebc8a9b0574ea2___init___py.html", "file": "app\\core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_config_py": {"hash": "8582c0b2e8a180dae1370494d7b334b0", "index": {"url": "z_adebc8a9b0574ea2_config_py.html", "file": "app\\core\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 59, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_exceptions_py": {"hash": "9fc0c71cc2426317ec27be8f8fdda24b", "index": {"url": "z_adebc8a9b0574ea2_exceptions_py.html", "file": "app\\core\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_logging_py": {"hash": "b47dad3d6a8aa769b47a0d6e0c232c58", "index": {"url": "z_adebc8a9b0574ea2_logging_py.html", "file": "app\\core\\logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "bbc1e8de942e9180a61cdf91c4c8175d", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 3, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_de3c7ac9f91c9430___init___py": {"hash": "0d9cf3bc5bd3b3d7e85e6f39ef8180a3", "index": {"url": "z_de3c7ac9f91c9430___init___py.html", "file": "app\\middleware\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_de3c7ac9f91c9430_logging_middleware_py": {"hash": "fc55fa400cc5b54f32dc444877e41e21", "index": {"url": "z_de3c7ac9f91c9430_logging_middleware_py.html", "file": "app\\middleware\\logging_middleware.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_de3c7ac9f91c9430_security_middleware_py": {"hash": "6fe029ae52e8e722b715f7da5cd96bb0", "index": {"url": "z_de3c7ac9f91c9430_security_middleware_py.html", "file": "app\\middleware\\security_middleware.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 58, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d___init___py": {"hash": "bb8c4b2b55483cb0d18ebfeb8056471f", "index": {"url": "z_1374716a89f3e08d___init___py.html", "file": "app\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_requests_py": {"hash": "8239374e3b9b91034ed7e46f906bb8a4", "index": {"url": "z_1374716a89f3e08d_requests_py.html", "file": "app\\models\\requests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 148, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_responses_py": {"hash": "82f69ec5fbe8f99dd52970aaf1329d27", "index": {"url": "z_1374716a89f3e08d_responses_py.html", "file": "app\\models\\responses.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_session_py": {"hash": "21cb19a99f7f3529fbad6867f9c90974", "index": {"url": "z_1374716a89f3e08d_session_py.html", "file": "app\\models\\session.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70___init___py": {"hash": "84df48a4c17d3c523dae6d6fd51526b4", "index": {"url": "z_4c37ce8615b5aa70___init___py.html", "file": "app\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_auth_service_py": {"hash": "a39618ea88cbd2e791890420cd62e086", "index": {"url": "z_4c37ce8615b5aa70_auth_service_py.html", "file": "app\\services\\auth_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_city_service_py": {"hash": "9054a432c1f90b2ad177ec1306475f10", "index": {"url": "z_4c37ce8615b5aa70_city_service_py.html", "file": "app\\services\\city_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 82, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_gemini_service_py": {"hash": "c567e47599a239f4e6ebd3cceb5392c8", "index": {"url": "z_4c37ce8615b5aa70_gemini_service_py.html", "file": "app\\services\\gemini_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 58, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_intent_service_py": {"hash": "ffdde2f57bc87c60f872e686b1074fd0", "index": {"url": "z_4c37ce8615b5aa70_intent_service_py.html", "file": "app\\services\\intent_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_session_service_py": {"hash": "a17fbe8abd4d84c020886acca5be97ae", "index": {"url": "z_4c37ce8615b5aa70_session_service_py.html", "file": "app\\services\\session_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 284, "n_excluded": 0, "n_missing": 264, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_settings_py": {"hash": "07913563d6796300edd20a8a27e5423e", "index": {"url": "z_5f5a17c013354698_settings_py.html", "file": "app\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1___init___py": {"hash": "ad3acb8ee918257b462f70198c057232", "index": {"url": "z_a7b07432402c05f1___init___py.html", "file": "app\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_security_py": {"hash": "defde6b2cbe8e24c37c16877a931b6fd", "index": {"url": "z_a7b07432402c05f1_security_py.html", "file": "app\\utils\\security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b_auth_py": {"hash": "1d9730d91fbb3366928a97c40b133545", "index": {"url": "z_4a9cca768ff3c21b_auth_py.html", "file": "app\\api\\auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}