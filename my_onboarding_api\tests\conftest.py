"""
Test configuration and fixtures.

This module provides common test fixtures and configuration.
"""

import os
import sys
import pytest
from unittest.mock import Mock, patch, MagicMock
from fastapi.testclient import TestClient
from importlib import import_module

# Add tests directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Mock only specific modules that cause issues
class MockModule:
    def __init__(self, *args, **kwargs):
        pass
    
    def __call__(self, *args, **kwargs):
        return MagicMock()

# Mock specific modules that cause issues during import
MOCKS = {
    'transformers': MagicMock(),
    'huggingface_hub': MagicMock(),
    'google.generativeai': MagicMock(),
    'app.services.gemini_service': MagicMock(),
}

# Apply the mocks
for module_name, mock in MOCKS.items():
    sys.modules[module_name] = mock

# Now import app modules
from app.main import create_app
from app.core.config import settings

# Import mock services
from tests.mocks.mock_intent_service import intent_service as mock_intent_service
from tests.mocks.mock_gemini_service import gemini_service as mock_gemini_service


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    app = create_app()
    return TestClient(app)


@pytest.fixture(autouse=True)
def mock_intent_service():
    """Mock intent classification service."""
    with patch('app.services.intent_service.intent_service', mock_intent_service):
        yield


@pytest.fixture(autouse=True)
def mock_gemini_service():
    """Mock Gemini AI service."""
    with patch('app.services.gemini_service.gemini_service', mock_gemini_service):
        yield


@pytest.fixture
def mock_auth_service():
    """Mock authentication service."""
    with patch('app.services.auth_service.auth_service') as mock:
        mock.login.return_value = {
            "status": "success",
            "message": "Login successful",
            "data": {"user_id": "123", "token": "test_token"}
        }
        mock.signup.return_value = {
            "status": "success",
            "message": "Signup completed successfully",
            "user": {"name": "Test User", "email": "<EMAIL>"}
        }
        yield mock


@pytest.fixture
def sample_message_request():
    """Sample message request data."""
    return {"message": "I want to create an account"}


@pytest.fixture
def sample_session_id():
    """Sample session ID for testing."""
    return "test-session-123"


@pytest.fixture
def test_headers(sample_session_id):
    """Test headers with session ID."""
    return {"session_id": sample_session_id}


@pytest.fixture
def mock_intent_model():
    """Mock the Hugging Face pipeline for intent classification."""
    # Create a mock pipeline that returns a callable
    class MockPipeline:
        def __init__(self, *args, **kwargs):
            pass
            
        def __call__(self, text):
            # Return a list of dicts that matches the expected format
            return [
                {"label": "signup", "score": 0.95},
                {"label": "login", "score": 0.05}
            ]
    
    # Patch the pipeline to return our mock
    with patch('transformers.pipeline', return_value=MockPipeline()) as mock:
        yield mock
